# Unified Integration System

This document describes the unified integration system that provides consistent behavior across all OAuth integrations.

## Overview

The system provides:
- **Consistent toast messages** with integration names
- **Automatic UI updates** when integrations connect/disconnect
- **Extensible architecture** for adding new integrations
- **Support for different OAuth patterns** (popup vs redirect)

## Architecture

### Core Components

1. **IntegrationStrategy Interface** (`types.ts`)
   - Defines the contract for all integrations
   - Supports both popup and redirect OAuth flows
   - Allows customization of display names and behavior

2. **useIntegrationActions Hook** (`hooks/useIntegrationActions.ts`)
   - Centralized logic for connect/disconnect actions
   - Handles success/error states consistently
   - Provides unified toast messages and UI updates

3. **Strategy Registry** (`registry.ts`)
   - Maps integration IDs to their strategies
   - Provides fallback to default strategy

### OAuth Flow Types

#### Popup-based OAuth (`oauthFlowType: "popup"`)
- Opens OAuth in a popup or modal
- Promise resolves when OAuth completes
- Success handling is automatic via `useIntegrationActions`
- **Example**: Google Calendar

```typescript
export const popupStrategy: IntegrationStrategy<Extra> = {
  id: "example",
  displayName: "Example Service",
  oauthFlowType: "popup",
  
  connect: async ({ apiClient, extra }) => {
    // Open popup, handle OAuth, resolve when complete
    return new Promise<void>((resolve, reject) => {
      // OAuth implementation
      resolve();
    });
  },
};
```

#### Redirect-based OAuth (`oauthFlowType: "redirect"`)
- Redirects entire page to OAuth provider
- Promise never resolves (page navigates away)
- Success handling via callback logic using `handleRedirectOAuthSuccess`
- **Example**: HubSpot

```typescript
export const redirectStrategy: IntegrationStrategy<Extra> = {
  id: "example",
  displayName: "Example Service", 
  oauthFlowType: "redirect",
  
  connect: async ({ extra }) => {
    // Redirect to OAuth URL - don't resolve promise
    window.location.href = extra.authUrl;
  },
};
```

## Behavior Consistency

### Success Messages
- **Before**: Inconsistent messages ("Connected successfully!" vs "HubSpot connected successfully!")
- **After**: Consistent format: `"${displayName} connected successfully!"`

### UI Updates
- **Before**: HubSpot auto-refreshed, Google Calendar didn't
- **After**: All integrations automatically refresh the integration list

### Error Messages
- **Before**: Generic error messages
- **After**: Integration-specific error messages: `"Failed to connect ${displayName}. Please try again."`

## Adding New Integrations

1. **Create Strategy File**
   ```typescript
   // strategies/newservice.strategy.ts
   export const newServiceStrategy: IntegrationStrategy<Extra> = {
     id: "new-service",
     displayName: "New Service",
     oauthFlowType: "popup", // or "redirect"
     
     connect: async ({ apiClient, extra, integration }) => {
       // Implementation
     },
   };
   ```

2. **Register Strategy**
   ```typescript
   // registry.ts
   export const registry = {
     [newServiceStrategy.id]: newServiceStrategy,
     // ... other strategies
   };
   ```

3. **For Redirect OAuth**: Use `handleRedirectOAuthSuccess` in callback logic
   ```typescript
   await handleRedirectOAuthSuccess("new-service", queryClient);
   ```

## Migration Notes

### HubSpot Changes
- Removed custom toast logic from strategy
- Now uses unified success handling via `handleRedirectOAuthSuccess`
- Added `displayName: "HubSpot"` and `oauthFlowType: "redirect"`

### Google Calendar Changes  
- Added `displayName: "Google Calendar"` and `oauthFlowType: "popup"`
- No functional changes needed (already worked correctly)

## Benefits

1. **Consistency**: All integrations behave the same way
2. **Maintainability**: Centralized success/error handling
3. **Extensibility**: Easy to add new integrations
4. **Type Safety**: TypeScript interfaces ensure correct implementation
5. **User Experience**: Predictable behavior across all integrations
