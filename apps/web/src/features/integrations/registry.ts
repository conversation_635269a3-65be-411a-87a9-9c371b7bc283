import { defaultStrategy } from "~/features/integrations/strategies/default.strategy";
import { googleCalendarStrategy } from "~/features/integrations/strategies/googleCalendar.strategy";
import { hubspotStrategy } from "~/features/integrations/strategies/hubspot.strategy";
import type { IntegrationStrategy } from "~/features/integrations/types";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const registry: Record<string, IntegrationStrategy<any>> = {
  [googleCalendarStrategy.id]: googleCalendarStrategy,
  [hubspotStrategy.id]: hubspotStrategy,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getStrategy(id: string): IntegrationStrategy<any> {
  return registry[id] ?? defaultStrategy;
}
