import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { useApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";
import { useDeleteIntegration } from "@pearl/api-hooks";

import { getStrategy } from "~/features/integrations/registry";

/**
 * Gets the display name for an integration, falling back to the integration name
 */
function getIntegrationDisplayName(integration: Integration, strategy: ReturnType<typeof getStrategy>): string {
  return strategy.displayName ?? integration.name;
}

/**
 * Handles post-connection success logic consistently across all integrations
 */
async function handleConnectionSuccess(
  integration: Integration,
  strategy: ReturnType<typeof getStrategy>,
  queryClient: ReturnType<typeof useQueryClient>
): Promise<void> {
  const displayName = getIntegrationDisplayName(integration, strategy);
  toast.success(`${displayName} connected successfully!`);
  await queryClient.invalidateQueries({ queryKey: ["integrations"] });
}

/**
 * Utility function for redirect-based OAuth flows to handle success consistently
 * This should be called from within strategy callback logic
 */
export async function handleRedirectOAuthSuccess(
  integrationId: string,
  queryClient: ReturnType<typeof useQueryClient>
): Promise<void> {
  const strategy = getStrategy(integrationId);
  // Create a minimal integration object for display name resolution
  const integration: Integration = {
    id: integrationId,
    name: strategy.displayName ?? integrationId,
    description: "",
    integrationType: "",
    source: "",
    isActive: false,
  };

  await handleConnectionSuccess(integration, strategy, queryClient);
}

export function useIntegrationActions(integration: Integration) {
  const { apiClient } = useApiClient();
  const strategy = getStrategy(integration.id);
  const extraHook = strategy.useExtraData?.() ?? {
    data: undefined,
    isLoading: false,
  };

  const queryClient = useQueryClient();

  const { mutate: connect, isPending: isConnecting } = useMutation({
    mutationFn: () =>
      strategy.connect({
        apiClient,
        extra: extraHook.data,
        integration,
      }),
    onSuccess: async () => {
      // Only handle success for popup-based OAuth flows
      // Redirect-based flows handle success in their own callback logic
      if (strategy.oauthFlowType !== "redirect") {
        await handleConnectionSuccess(integration, strategy, queryClient);
      }
    },
    onError: () => {
      const displayName = getIntegrationDisplayName(integration, strategy);
      toast.error(`Failed to connect ${displayName}. Please try again.`);
    },
  });

  const { mutate: genericDisconnect, isPending: isDisconnecting } =
    useDeleteIntegration();

  const disconnect = () => {
    const displayName = getIntegrationDisplayName(integration, strategy);

    if (strategy.disconnect) {
      return strategy
        .disconnect({ apiClient, integration })
        .then(async () => {
          toast.success(`${displayName} disconnected successfully!`);
          await queryClient.invalidateQueries({ queryKey: ["integrations"] });
        })
        .catch(() =>
          toast.error(`Failed to disconnect ${displayName}. Please try again.`),
        );
    }

    // fallback to generic endpoint
    genericDisconnect(integration.id, {
      onSuccess: () => {
        toast.success(`${displayName} disconnected successfully!`);
        void queryClient.invalidateQueries({ queryKey: ["integrations"] });
      },
      onError: () =>
        toast.error(`Failed to disconnect ${displayName}. Please try again.`),
    });
  };

  return {
    connect,
    disconnect,
    isConnecting,
    isDisconnecting,
    extraLoading: extraHook.isLoading,
  };
}
