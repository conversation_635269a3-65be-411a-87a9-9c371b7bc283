import type { ApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";

export type OAuthFlowType = "redirect" | "popup";

/**
 * Integration strategy interface for implementing OAuth flows consistently.
 *
 * This interface provides a unified way to handle different OAuth integration patterns:
 *
 * - **Popup-based OAuth** (`oauthFlowType: "popup"`):
 *   - Opens OAuth in a popup/modal
 *   - Promise resolves when OAuth completes
 *   - Success handling is done by useIntegrationActions
 *   - Example: Google Calendar
 *
 * - **Redirect-based OAuth** (`oauthFlowType: "redirect"`):
 *   - Redirects entire page to OAuth provider
 *   - Promise never resolves (page navigates away)
 *   - Success handling is done by strategy's callback logic using handleRedirectOAuthSuccess
 *   - Example: HubSpot
 *
 * @template Extra - Type for additional data needed by the strategy (e.g., auth URLs)
 */
export interface IntegrationStrategy<Extra = unknown> {
  /** Unique identifier for the integration */
  id: string;

  /** Display name for the integration (used in success messages). Defaults to integration.name */
  displayName?: string;

  /** OAuth flow type - determines how success is handled. Defaults to "popup" */
  oauthFlowType?: OAuthFlowType;

  useExtraData?: () => {
    data: Extra | undefined;
    isLoading: boolean;
  };

  connect: (params: {
    apiClient: ApiClient;
    extra: Extra | undefined;
    integration: Integration;
  }) => Promise<void>;

  disconnect?: (params: {
    apiClient: ApiClient;
    integration: Integration;
  }) => Promise<void>;
}
