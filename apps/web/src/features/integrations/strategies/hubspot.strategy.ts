import { useEffect, useRef } from "react";
import { toast } from "sonner";

import type { ApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";
import { useGetHubspotAuthUrl } from "@pearl/api-hooks";
import { useQueryClient } from "@tanstack/react-query";

import { handleRedirectOAuthSuccess } from "~/features/integrations/hooks/useIntegrationActions";
import type { IntegrationStrategy } from "~/features/integrations/types";

interface Extra {
  authUrl: string;
}

const useHubspotAutoCallback = () => {
  const queryClient = useQueryClient();
  const hasProcessed = useRef(false);

  useEffect(() => {
    const processCallback = async () => {
      if (hasProcessed.current) return;

      const currentUrl = new URL(window.location.href);
      const code = currentUrl.searchParams.get("code");
      const state = currentUrl.searchParams.get("state");

      if (code && state && currentUrl.pathname === "/settings") {
        hasProcessed.current = true;

        try {
          const { apiClient } = await import("~/services/api/ApiClient");

          const encodedCode = encodeURIComponent(code);
          const encodedState = encodeURIComponent(state);

          await apiClient.get(
            `/workspace/hubspot/callback?code=${encodedCode}&state=${encodedState}`,
          );

          const newSearchParams = new URLSearchParams(currentUrl.search);
          newSearchParams.delete("code");
          newSearchParams.delete("state");

          const newUrl = newSearchParams.toString()
            ? `/settings?${newSearchParams.toString()}`
            : "/settings";

          window.history.replaceState({}, "", newUrl);

          // Use unified success handling
          await handleRedirectOAuthSuccess("hubspot", queryClient);
        } catch (error) {
          console.error("HubSpot callback error:", error);
          toast.error("Failed to connect HubSpot. Please try again.");
        }
      }
    };

    void processCallback();
  }, [queryClient]);
};

export const hubspotStrategy: IntegrationStrategy<Extra> = {
  id: "hubspot",
  displayName: "HubSpot",
  oauthFlowType: "redirect",

  useExtraData: () => {
    // Use the auto-callback hook to process OAuth callbacks automatically
    useHubspotAutoCallback();

    return useGetHubspotAuthUrl();
  },

  connect: ({
    extra,
  }: {
    apiClient: ApiClient;
    extra: Extra | undefined;
    integration: Integration;
  }) =>
    new Promise<void>((_, reject) => {
      if (!extra?.authUrl) {
        reject(new Error("No auth URL available"));
        return;
      }

      // Redirect to OAuth URL - the promise will never resolve
      // because the page will navigate away. The success toast
      // will be shown by useHubspotAutoCallback after OAuth completion.
      window.location.href = extra.authUrl;

      // Don't resolve here - let the page redirect handle the flow
    }),
};
