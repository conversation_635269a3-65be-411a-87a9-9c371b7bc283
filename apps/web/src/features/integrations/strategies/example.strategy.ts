/**
 * Example integration strategy demonstrating how to add new integrations
 * with consistent behavior using the unified integration system.
 * 
 * This file serves as a template for adding new OAuth integrations.
 * Simply copy this file, rename it, and customize the implementation.
 */

import type { ApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";

import type { IntegrationStrategy } from "~/features/integrations/types";

interface ExampleExtra {
  authUrl: string;
}

/**
 * Example strategy for a popup-based OAuth integration
 * 
 * For popup-based OAuth:
 * 1. Set oauthFlowType: "popup"
 * 2. Resolve the promise when OAuth completes
 * 3. Success handling is automatic via useIntegrationActions
 */
export const examplePopupStrategy: IntegrationStrategy<ExampleExtra> = {
  id: "example-popup",
  displayName: "Example Popup Service",
  oauthFlowType: "popup",

  // Optional: provide extra data needed for OAuth
  useExtraData: () => ({
    data: { authUrl: "https://example.com/oauth" },
    isLoading: false,
  }),

  connect: async ({ apiClient, extra, integration }) => {
    if (!extra?.authUrl) {
      throw new Error("No auth URL available");
    }

    // Simulate popup-based OAuth flow
    return new Promise<void>((resolve, reject) => {
      // In a real implementation, you would:
      // 1. Open OAuth popup/modal
      // 2. Handle OAuth callback
      // 3. Call API endpoint with OAuth code
      // 4. Resolve promise when complete

      // Example simulation:
      setTimeout(async () => {
        try {
          await apiClient.get(`/workspace/example/callback?code=example_code`);
          resolve();
        } catch (error) {
          reject(error);
        }
      }, 1000);
    });
  },
};

/**
 * Example strategy for a redirect-based OAuth integration
 * 
 * For redirect-based OAuth:
 * 1. Set oauthFlowType: "redirect"
 * 2. Don't resolve the promise (page will redirect away)
 * 3. Handle success in callback logic using handleRedirectOAuthSuccess
 */
export const exampleRedirectStrategy: IntegrationStrategy<ExampleExtra> = {
  id: "example-redirect",
  displayName: "Example Redirect Service",
  oauthFlowType: "redirect",

  useExtraData: () => ({
    data: { authUrl: "https://example.com/oauth" },
    isLoading: false,
  }),

  connect: async ({ extra }) => {
    if (!extra?.authUrl) {
      throw new Error("No auth URL available");
    }

    // Redirect to OAuth URL - promise never resolves
    window.location.href = extra.authUrl;
    
    // Don't resolve - let the redirect handle the flow
    // Success will be handled by callback logic using handleRedirectOAuthSuccess
  },
};

/**
 * Example callback hook for redirect-based OAuth
 * 
 * This would be used in the useExtraData function for redirect-based strategies
 * to automatically process OAuth callbacks when the user returns from the OAuth provider.
 */
/*
import { useEffect, useRef } from "react";
import { useQueryClient } from "@tanstack/react-query";
import { handleRedirectOAuthSuccess } from "~/features/integrations/hooks/useIntegrationActions";

const useExampleAutoCallback = () => {
  const queryClient = useQueryClient();
  const hasProcessed = useRef(false);

  useEffect(() => {
    const processCallback = async () => {
      if (hasProcessed.current) return;

      const currentUrl = new URL(window.location.href);
      const code = currentUrl.searchParams.get("code");
      const state = currentUrl.searchParams.get("state");

      if (code && state && currentUrl.pathname === "/settings") {
        hasProcessed.current = true;

        try {
          const { apiClient } = await import("~/services/api/ApiClient");
          
          await apiClient.get(
            `/workspace/example/callback?code=${encodeURIComponent(code)}&state=${encodeURIComponent(state)}`
          );

          // Clean up URL
          const newSearchParams = new URLSearchParams(currentUrl.search);
          newSearchParams.delete("code");
          newSearchParams.delete("state");
          const newUrl = newSearchParams.toString() ? `/settings?${newSearchParams.toString()}` : "/settings";
          window.history.replaceState({}, "", newUrl);

          // Use unified success handling
          await handleRedirectOAuthSuccess("example-redirect", queryClient);
        } catch (error) {
          console.error("Example callback error:", error);
          toast.error("Failed to connect Example Service. Please try again.");
        }
      }
    };

    void processCallback();
  }, [queryClient]);
};
*/
