import { ErrorStep } from "~/features/salesforce/components/ErrorStep";
import { LoadingStep } from "~/features/salesforce/components/LoadingStep";
import { SuccessStep } from "~/features/salesforce/components/SuccessStep";

interface SalesforceCallbackContentProps {
  isLoading: boolean;
  isImporting: boolean;
  isSuccess: boolean;
  isError: boolean;
  errorMessage: string | null;
}

export const SalesforceCallbackContent = ({
  isLoading,
  isImporting,
  isSuccess,
  isError,
  errorMessage,
}: SalesforceCallbackContentProps) => {
  let content = null;

  if (isLoading) {
    content = (
      <LoadingStep
        title="Processing Salesforce connection..."
        stage="connecting"
      />
    );
  } else if (isImporting) {
    content = (
      <LoadingStep title="Synchronizing your accounts..." stage="importing" />
    );
  } else if (isSuccess) {
    content = <SuccessStep />;
  } else if (isError) {
    content = <ErrorStep errorMessage={errorMessage} />;
  }

  if (!content) {
    return null;
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-6">
      {content}
    </div>
  );
};
