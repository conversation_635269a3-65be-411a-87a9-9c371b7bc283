import { useCallback, useRef } from "react";

import { useApiClient } from "@pearl/api-client";

export const useHubspotCallback = () => {
  const { apiClient } = useApiClient();

  const hasProcessed = useRef(false);

  const processCallback = useCallback(
    async (code: string, state: string) => {
      if (hasProcessed.current || !code || !state) {
        return;
      }

      hasProcessed.current = true;

      try {
        const encodedCode = encodeURIComponent(code);
        const encodedState = encodeURIComponent(state);

        await apiClient.get(
          `/workspace/hubspot/callback?code=${encodedCode}&state=${encodedState}`,
        );
      } catch (error) {
        console.log(`🚀 ~ error:`, error);
      }
    },
    [apiClient],
  );

  return {
    processCallback,
  };
};